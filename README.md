# DeepSeek Website Wrapper

A Python CLI tool that automates interaction with DeepSeek's chat interface using undetected chromedriver with session persistence.

## Quick Start

```bash
# Install dependencies
pip3 install -r requirements.txt

# Send a prompt
python3 deepseek_wrapper.py --prompt "Hello! What is 2+2?"

# Or use a file
python3 deepseek_wrapper.py --prompt-file test_prompt.txt
```

## How it works

1. **Session Persistence**: Maintains cookies and local storage across runs
2. **Navigate**: Goes to https://chat.deepseek.com/
3. **Wait for Load**: Waits for "Hi, I'm DeepSeek." text to appear
4. **Send Prompt**: Submits your prompt to the chat input
5. **Monitor Response**: Watches the "New chat" button text changes to detect when response generation starts and completes
6. **Extract Response**: Clicks the copy button and reads from clipboard
7. **Output**: Prints the response to stdout

## Response Detection Logic

The tool uses a reliable method to detect response completion:

1. **Send prompt**: Submits the prompt and hits Enter
2. **Wait for button**: The "New chat" button appears after ~1 second
3. **Find button**: Locates the button (`<div tabindex="0">New chat</div>`)
4. **Monitor changes**: Watches the button text every second
5. **Response complete**: When text changes from "New chat" to the actual response content
6. **Extract response**: Clicks the copy button and reads from clipboard

This method is much more reliable than the previous file input detection.
