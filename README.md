# Warning

This modifies the system clipboard to both paste in the text and retrieve the text from the website, so take care that you do not modify the clipboard during execution.

---

Previous approaches of not using the system clipboard were unreliable, as they were either missing lines or were not inserted at all.

# Usage

```bash
# Install dependencies
pip3 install -r requirements.txt

# Send a prompt
python3 deepseek_wrapper.py --prompt "Hello! What is 2+2?"

# Or use a file
python3 deepseek_wrapper.py --prompt-file test_prompt.txt
```

# How it works

1. **Session Persistence**: Maintains cookies and local storage across runs
2. **Navigate**: Goes to https://chat.deepseek.com/
3. **Wait for Load**: Waits for "Hi, I'm DeepSeek." text to appear
4. **Send Prompt**: Submits your prompt to the chat input
5. **Monitor Response**: Watches the "New chat" button text changes to detect when response generation starts and completes
6. **Extract Response**: Clicks the copy button and reads from clipboard
7. **Output**: Prints the response to stdout
